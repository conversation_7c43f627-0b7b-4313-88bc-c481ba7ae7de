globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/UserInfoPopover.module.css?modules": function(module, exports, __mako_require__) {},
        "src/pages/personal-center/UserInfoPopover.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
            var _UserInfoPopovermodulecssasmodule = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/UserInfoPopover.module.css?asmodule"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            const { Text } = _antd.Typography;
            /**
 * 用户信息气泡卡片组件
 *
 * 在用户名上显示详细的用户信息，包括电话、邮箱、注册时间等。
 * 采用Popover组件实现悬浮显示效果。
 *
 * 主要功能：
 * 1. 显示用户邮箱
 * 2. 显示用户电话
 * 3. 显示注册时间
 * 4. 显示最后登录时间
 * 5. 显示最后登录团队
 *
 * 使用方式：
 * <UserInfoPopover userInfo={userInfo}>
 *   <span>用户名</span>
 * </UserInfoPopover>
 */ const UserInfoPopover = ({ userInfo, children })=>{
                const popoverContent = /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    className: _UserInfoPopovermodulecssasmodule.default.popoverContent,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        direction: "vertical",
                        size: 12,
                        style: {
                            width: '100%'
                        },
                        children: [
                            userInfo.email && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                className: `${_UserInfoPopovermodulecssasmodule.default.infoItem} ${_UserInfoPopovermodulecssasmodule.default.email}`,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.iconWrapper,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {
                                            className: _UserInfoPopovermodulecssasmodule.default.icon,
                                            style: {
                                                color: '#1890ff'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                            lineNumber: 54,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 53,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.infoContent,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                className: _UserInfoPopovermodulecssasmodule.default.label,
                                                children: "邮箱"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 57,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                className: _UserInfoPopovermodulecssasmodule.default.value,
                                                copyable: true,
                                                children: userInfo.email
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 60,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 56,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                lineNumber: 52,
                                columnNumber: 11
                            }, this),
                            userInfo.telephone && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                className: `${_UserInfoPopovermodulecssasmodule.default.infoItem} ${_UserInfoPopovermodulecssasmodule.default.phone}`,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.iconWrapper,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PhoneOutlined, {
                                            className: _UserInfoPopovermodulecssasmodule.default.icon,
                                            style: {
                                                color: '#52c41a'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                            lineNumber: 70,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 69,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.infoContent,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                className: _UserInfoPopovermodulecssasmodule.default.label,
                                                children: "电话"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 73,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                className: _UserInfoPopovermodulecssasmodule.default.value,
                                                copyable: true,
                                                children: userInfo.telephone
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 76,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 72,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                lineNumber: 68,
                                columnNumber: 11
                            }, this),
                            (userInfo.email || userInfo.telephone) && userInfo.registerDate && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                className: _UserInfoPopovermodulecssasmodule.default.divider
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                lineNumber: 84,
                                columnNumber: 11
                            }, this),
                            userInfo.registerDate && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                className: `${_UserInfoPopovermodulecssasmodule.default.infoItem} ${_UserInfoPopovermodulecssasmodule.default.register}`,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.iconWrapper,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                            className: _UserInfoPopovermodulecssasmodule.default.icon,
                                            style: {
                                                color: '#722ed1'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                            lineNumber: 91,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 90,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.infoContent,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                className: _UserInfoPopovermodulecssasmodule.default.label,
                                                children: "注册时间"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 94,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                className: _UserInfoPopovermodulecssasmodule.default.value,
                                                children: userInfo.registerDate
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 97,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 93,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                lineNumber: 89,
                                columnNumber: 11
                            }, this),
                            userInfo.lastLoginTime && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                className: `${_UserInfoPopovermodulecssasmodule.default.infoItem} ${_UserInfoPopovermodulecssasmodule.default.lastLogin}`,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.iconWrapper,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                            className: _UserInfoPopovermodulecssasmodule.default.icon,
                                            style: {
                                                color: '#fa8c16'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                            lineNumber: 107,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 106,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.infoContent,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                className: _UserInfoPopovermodulecssasmodule.default.label,
                                                children: "最后登录"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 110,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                className: _UserInfoPopovermodulecssasmodule.default.value,
                                                children: userInfo.lastLoginTime
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 113,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 109,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                lineNumber: 105,
                                columnNumber: 11
                            }, this),
                            userInfo.lastLoginTeam && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                className: `${_UserInfoPopovermodulecssasmodule.default.infoItem} ${_UserInfoPopovermodulecssasmodule.default.team}`,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.iconWrapper,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                            className: _UserInfoPopovermodulecssasmodule.default.icon,
                                            style: {
                                                color: '#13c2c2'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                            lineNumber: 123,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 122,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.infoContent,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                className: _UserInfoPopovermodulecssasmodule.default.label,
                                                children: "登录团队"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 126,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                className: _UserInfoPopovermodulecssasmodule.default.value,
                                                children: userInfo.lastLoginTeam
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 129,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 125,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                lineNumber: 121,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                        lineNumber: 49,
                        columnNumber: 7
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                    lineNumber: 48,
                    columnNumber: 5
                }, this);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popover, {
                    content: popoverContent,
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        className: _UserInfoPopovermodulecssasmodule.default.popoverTitle,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            strong: true,
                            children: "用户详细信息"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                            lineNumber: 144,
                            columnNumber: 11
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                        lineNumber: 143,
                        columnNumber: 9
                    }, void 0),
                    trigger: [
                        "hover",
                        "click"
                    ],
                    placement: "bottomLeft",
                    styles: {
                        body: {
                            padding: '16px 20px',
                            borderRadius: '12px',
                            background: '#ffffff',
                            maxWidth: '380px',
                            boxShadow: '0 12px 32px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08)',
                            border: '1px solid rgba(0, 0, 0, 0.06)'
                        }
                    },
                    arrow: {
                        pointAtCenter: true
                    },
                    mouseEnterDelay: 0.3,
                    mouseLeaveDelay: 0.1,
                    fresh: false,
                    zIndex: 1060,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                        className: _UserInfoPopovermodulecssasmodule.default.trigger,
                        children: children
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                        lineNumber: 167,
                        columnNumber: 7
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                    lineNumber: 140,
                    columnNumber: 5
                }, this);
            };
            _c = UserInfoPopover;
            var _default = UserInfoPopover;
            var _c;
            $RefreshReg$(_c, "UserInfoPopover");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/personal-center/UserInfoPopover.module.css?asmodule": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            "";
            var _default = {
                "trigger": `trigger-AR_BTHjV`,
                "email": `email-i9QUimb9`,
                "phone": `phone-IA4B8_HE`,
                "label": `label-ehWFee0h`,
                "value": `value-Zl-mRo9s`,
                "popoverContent": `popoverContent-ZvwtMgOV`,
                "divider": `divider-vyN6VYw3`,
                "lastLogin": `lastLogin-VCyens7z`,
                "popoverTitle": `popoverTitle-JPCpFP3I`,
                "settingIcon": `settingIcon-ijupiTrh`,
                "team": `team-lHzntWgR`,
                "iconWrapper": `iconWrapper-5vz1D_9p`,
                "icon": `icon-_OERA8Hx`,
                "fadeIn": `fadeIn-8DvHJTuh`,
                "register": `register-VT8PxSJM`,
                "infoItem": `infoItem-6xYKGZ8Q`,
                "infoContent": `infoContent-vsma-SV-`,
                "questionIcon": `questionIcon-hRheYLsO`
            };
        }
    }
}, function(runtime) {
    runtime._h = '15565379179614218682';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.8617566296289234220.hot-update.js.map