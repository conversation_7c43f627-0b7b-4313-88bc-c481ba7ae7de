{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.8617566296289234220.hot-update.js", "src/pages/personal-center/UserInfoPopover.tsx", "H:/projects/IdeaProjects/teamAuth/frontend/src/pages/personal-center/UserInfoPopover.module.css?asmodule"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='15565379179614218682';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\n  MailOutlined,\n  PhoneOutlined,\n  CalendarOutlined,\n  ClockCircleOutlined,\n  TeamOutlined,\n} from '@ant-design/icons';\nimport {\n  Popover,\n  Space,\n  Typography,\n  Divider,\n} from 'antd';\nimport React from 'react';\nimport type { UserProfileDetailResponse } from '@/types/api';\nimport styles from './UserInfoPopover.module.css';\n\nconst { Text } = Typography;\n\ninterface UserInfoPopoverProps {\n  userInfo: UserProfileDetailResponse;\n  children: React.ReactNode;\n}\n\n/**\n * 用户信息气泡卡片组件\n *\n * 在用户名上显示详细的用户信息，包括电话、邮箱、注册时间等。\n * 采用Popover组件实现悬浮显示效果。\n *\n * 主要功能：\n * 1. 显示用户邮箱\n * 2. 显示用户电话\n * 3. 显示注册时间\n * 4. 显示最后登录时间\n * 5. 显示最后登录团队\n *\n * 使用方式：\n * <UserInfoPopover userInfo={userInfo}>\n *   <span>用户名</span>\n * </UserInfoPopover>\n */\nconst UserInfoPopover: React.FC<UserInfoPopoverProps> = ({\n  userInfo,\n  children,\n}) => {\n  const popoverContent = (\n    <div className={styles.popoverContent}>\n      <Space direction=\"vertical\" size={12} style={{ width: '100%' }}>\n        {/* 联系信息 */}\n        {userInfo.email && (\n          <div className={`${styles.infoItem} ${styles.email}`}>\n            <div className={styles.iconWrapper}>\n              <MailOutlined className={styles.icon} style={{ color: '#1890ff' }} />\n            </div>\n            <div className={styles.infoContent}>\n              <Text type=\"secondary\" className={styles.label}>\n                邮箱\n              </Text>\n              <Text className={styles.value} copyable>\n                {userInfo.email}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {userInfo.telephone && (\n          <div className={`${styles.infoItem} ${styles.phone}`}>\n            <div className={styles.iconWrapper}>\n              <PhoneOutlined className={styles.icon} style={{ color: '#52c41a' }} />\n            </div>\n            <div className={styles.infoContent}>\n              <Text type=\"secondary\" className={styles.label}>\n                电话\n              </Text>\n              <Text className={styles.value} copyable>\n                {userInfo.telephone}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {(userInfo.email || userInfo.telephone) && userInfo.registerDate && (\n          <Divider className={styles.divider} />\n        )}\n\n        {/* 时间信息 */}\n        {userInfo.registerDate && (\n          <div className={`${styles.infoItem} ${styles.register}`}>\n            <div className={styles.iconWrapper}>\n              <CalendarOutlined className={styles.icon} style={{ color: '#722ed1' }} />\n            </div>\n            <div className={styles.infoContent}>\n              <Text type=\"secondary\" className={styles.label}>\n                注册时间\n              </Text>\n              <Text className={styles.value}>\n                {userInfo.registerDate}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {userInfo.lastLoginTime && (\n          <div className={`${styles.infoItem} ${styles.lastLogin}`}>\n            <div className={styles.iconWrapper}>\n              <ClockCircleOutlined className={styles.icon} style={{ color: '#fa8c16' }} />\n            </div>\n            <div className={styles.infoContent}>\n              <Text type=\"secondary\" className={styles.label}>\n                最后登录\n              </Text>\n              <Text className={styles.value}>\n                {userInfo.lastLoginTime}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {userInfo.lastLoginTeam && (\n          <div className={`${styles.infoItem} ${styles.team}`}>\n            <div className={styles.iconWrapper}>\n              <TeamOutlined className={styles.icon} style={{ color: '#13c2c2' }} />\n            </div>\n            <div className={styles.infoContent}>\n              <Text type=\"secondary\" className={styles.label}>\n                登录团队\n              </Text>\n              <Text className={styles.value}>\n                {userInfo.lastLoginTeam}\n              </Text>\n            </div>\n          </div>\n        )}\n      </Space>\n    </div>\n  );\n\n  return (\n    <Popover\n      content={popoverContent}\n      title={\n        <div className={styles.popoverTitle}>\n          <Text strong>用户详细信息</Text>\n        </div>\n      }\n      trigger={[\"hover\", \"click\"]}\n      placement=\"bottomLeft\"\n      styles={{\n        body: {\n          padding: '16px 20px',\n          borderRadius: '12px',\n          background: '#ffffff',\n          maxWidth: '380px',\n          boxShadow: '0 12px 32px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08)',\n          border: '1px solid rgba(0, 0, 0, 0.06)',\n        },\n      }}\n      arrow={{\n        pointAtCenter: true,\n      }}\n      mouseEnterDelay={0.3}\n      mouseLeaveDelay={0.1}\n      fresh={false}\n      zIndex={1060}\n    >\n      <span className={styles.trigger}>\n        {children}\n      </span>\n    </Popover>\n  );\n};\n\nexport default UserInfoPopover;\n", "\nimport \"H:/projects/IdeaProjects/teamAuth/frontend/src/pages/personal-center/UserInfoPopover.module.css?modules\";\nexport default {\"trigger\": `trigger-AR_BTHjV`,\"email\": `email-i9QUimb9`,\"phone\": `phone-IA4B8_HE`,\"label\": `label-ehWFee0h`,\"value\": `value-Zl-mRo9s`,\"popoverContent\": `popoverContent-ZvwtMgOV`,\"divider\": `divider-vyN6VYw3`,\"lastLogin\": `lastLogin-VCyens7z`,\"popoverTitle\": `popoverTitle-JPCpFP3I`,\"settingIcon\": `settingIcon-ijupiTrh`,\"team\": `team-lHzntWgR`,\"iconWrapper\": `iconWrapper-5vz1D_9p`,\"icon\": `icon-_OERA8Hx`,\"fadeIn\": `fadeIn-8DvHJTuh`,\"register\": `register-VT8PxSJM`,\"infoItem\": `infoItem-6xYKGZ8Q`,\"infoContent\": `infoContent-vsma-SV-`,\"questionIcon\": `questionIcon-hRheYLsO`}\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;;wCC0Kb;;;2BAAA;;;;;;;0CAvKO;yCAMA;mFACW;8GAEC;;;;;;;;;YAEnB,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;YAO3B;;;;;;;;;;;;;;;;;CAiBC,GACD,MAAM,kBAAkD,CAAC,EACvD,QAAQ,EACR,QAAQ,EACT;gBACC,MAAM,+BACJ,2BAAC;oBAAI,WAAW,yCAAM,CAAC,cAAc;8BACnC,cAAA,2BAAC,WAAK;wBAAC,WAAU;wBAAW,MAAM;wBAAI,OAAO;4BAAE,OAAO;wBAAO;;4BAE1D,SAAS,KAAK,kBACb,2BAAC;gCAAI,WAAW,CAAC,EAAE,yCAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,yCAAM,CAAC,KAAK,CAAC,CAAC;;kDAClD,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;kDAChC,cAAA,2BAAC,mBAAY;4CAAC,WAAW,yCAAM,CAAC,IAAI;4CAAE,OAAO;gDAAE,OAAO;4CAAU;;;;;;;;;;;kDAElE,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;;0DAChC,2BAAC;gDAAK,MAAK;gDAAY,WAAW,yCAAM,CAAC,KAAK;0DAAE;;;;;;0DAGhD,2BAAC;gDAAK,WAAW,yCAAM,CAAC,KAAK;gDAAE,QAAQ;0DACpC,SAAS,KAAK;;;;;;;;;;;;;;;;;;4BAMtB,SAAS,SAAS,kBACjB,2BAAC;gCAAI,WAAW,CAAC,EAAE,yCAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,yCAAM,CAAC,KAAK,CAAC,CAAC;;kDAClD,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;kDAChC,cAAA,2BAAC,oBAAa;4CAAC,WAAW,yCAAM,CAAC,IAAI;4CAAE,OAAO;gDAAE,OAAO;4CAAU;;;;;;;;;;;kDAEnE,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;;0DAChC,2BAAC;gDAAK,MAAK;gDAAY,WAAW,yCAAM,CAAC,KAAK;0DAAE;;;;;;0DAGhD,2BAAC;gDAAK,WAAW,yCAAM,CAAC,KAAK;gDAAE,QAAQ;0DACpC,SAAS,SAAS;;;;;;;;;;;;;;;;;;4BAMzB,CAAA,SAAS,KAAK,IAAI,SAAS,SAAS,AAAD,KAAM,SAAS,YAAY,kBAC9D,2BAAC,aAAO;gCAAC,WAAW,yCAAM,CAAC,OAAO;;;;;;4BAInC,SAAS,YAAY,kBACpB,2BAAC;gCAAI,WAAW,CAAC,EAAE,yCAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,yCAAM,CAAC,QAAQ,CAAC,CAAC;;kDACrD,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;kDAChC,cAAA,2BAAC,uBAAgB;4CAAC,WAAW,yCAAM,CAAC,IAAI;4CAAE,OAAO;gDAAE,OAAO;4CAAU;;;;;;;;;;;kDAEtE,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;;0DAChC,2BAAC;gDAAK,MAAK;gDAAY,WAAW,yCAAM,CAAC,KAAK;0DAAE;;;;;;0DAGhD,2BAAC;gDAAK,WAAW,yCAAM,CAAC,KAAK;0DAC1B,SAAS,YAAY;;;;;;;;;;;;;;;;;;4BAM7B,SAAS,aAAa,kBACrB,2BAAC;gCAAI,WAAW,CAAC,EAAE,yCAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,yCAAM,CAAC,SAAS,CAAC,CAAC;;kDACtD,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;kDAChC,cAAA,2BAAC,0BAAmB;4CAAC,WAAW,yCAAM,CAAC,IAAI;4CAAE,OAAO;gDAAE,OAAO;4CAAU;;;;;;;;;;;kDAEzE,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;;0DAChC,2BAAC;gDAAK,MAAK;gDAAY,WAAW,yCAAM,CAAC,KAAK;0DAAE;;;;;;0DAGhD,2BAAC;gDAAK,WAAW,yCAAM,CAAC,KAAK;0DAC1B,SAAS,aAAa;;;;;;;;;;;;;;;;;;4BAM9B,SAAS,aAAa,kBACrB,2BAAC;gCAAI,WAAW,CAAC,EAAE,yCAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,yCAAM,CAAC,IAAI,CAAC,CAAC;;kDACjD,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;kDAChC,cAAA,2BAAC,mBAAY;4CAAC,WAAW,yCAAM,CAAC,IAAI;4CAAE,OAAO;gDAAE,OAAO;4CAAU;;;;;;;;;;;kDAElE,2BAAC;wCAAI,WAAW,yCAAM,CAAC,WAAW;;0DAChC,2BAAC;gDAAK,MAAK;gDAAY,WAAW,yCAAM,CAAC,KAAK;0DAAE;;;;;;0DAGhD,2BAAC;gDAAK,WAAW,yCAAM,CAAC,KAAK;0DAC1B,SAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBASrC,qBACE,2BAAC,aAAO;oBACN,SAAS;oBACT,qBACE,2BAAC;wBAAI,WAAW,yCAAM,CAAC,YAAY;kCACjC,cAAA,2BAAC;4BAAK,MAAM;sCAAC;;;;;;;;;;;oBAGjB,SAAS;wBAAC;wBAAS;qBAAQ;oBAC3B,WAAU;oBACV,QAAQ;wBACN,MAAM;4BACJ,SAAS;4BACT,cAAc;4BACd,YAAY;4BACZ,UAAU;4BACV,WAAW;4BACX,QAAQ;wBACV;oBACF;oBACA,OAAO;wBACL,eAAe;oBACjB;oBACA,iBAAiB;oBACjB,iBAAiB;oBACjB,OAAO;oBACP,QAAQ;8BAER,cAAA,2BAAC;wBAAK,WAAW,yCAAM,CAAC,OAAO;kCAC5B;;;;;;;;;;;YAIT;iBAjIM;gBAmIN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCC3Kf;;;2BAAA;;;;gBAAA,WAAe;gBAAC,WAAW,CAAC,gBAAgB,CAAC;gBAAC,SAAS,CAAC,cAAc,CAAC;gBAAC,SAAS,CAAC,cAAc,CAAC;gBAAC,SAAS,CAAC,cAAc,CAAC;gBAAC,SAAS,CAAC,cAAc,CAAC;gBAAC,kBAAkB,CAAC,uBAAuB,CAAC;gBAAC,WAAW,CAAC,gBAAgB,CAAC;gBAAC,aAAa,CAAC,kBAAkB,CAAC;gBAAC,gBAAgB,CAAC,qBAAqB,CAAC;gBAAC,eAAe,CAAC,oBAAoB,CAAC;gBAAC,QAAQ,CAAC,aAAa,CAAC;gBAAC,eAAe,CAAC,oBAAoB,CAAC;gBAAC,QAAQ,CAAC,aAAa,CAAC;gBAAC,UAAU,CAAC,eAAe,CAAC;gBAAC,YAAY,CAAC,iBAAiB,CAAC;gBAAC,YAAY,CAAC,iBAAiB,CAAC;gBAAC,eAAe,CAAC,oBAAoB,CAAC;gBAAC,gBAAgB,CAAC,qBAAqB,CAAC;YAAA;;IFCjkB;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}