globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/pages/personal-center/DataOverview.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _user = __mako_require__("src/services/user.ts");
            var _team = __mako_require__("src/services/team.ts");
            var _todo = __mako_require__("src/services/todo.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            /**
 * 数据概览卡片组件
 *
 * 使用 Ant Design Pro Components 的 StatisticCard 组件显示用户的个人统计数据，
 * 采用响应式网格布局适配不同屏幕尺寸。包括车辆、人员、预警、告警等指标的统计卡片。
 *
 * 主要功能：
 * 1. 显示车辆数量统计 - 使用车辆图标，蓝色主题
 * 2. 显示人员数量统计 - 使用用户组图标，绿色主题
 * 3. 显示预警数量统计 - 使用感叹号图标，橙色主题
 * 4. 显示告警数量统计 - 使用警告图标，红色主题
 *
 * 数据来源：
 * - 个人统计数据：通过UserService.getUserPersonalStats()获取
 *
 * 响应式布局特点：
 * - xs (< 576px): 2x2 网格布局，移动端优化
 * - sm (≥ 576px): 2x2 网格布局，小屏设备
 * - md (≥ 768px): 1x4 水平排列，平板优化
 * - lg+ (≥ 992px): 1x4 水平排列，桌面端
 * - 每个统计项都有语义化的图标和颜色主题
 * - 统一的卡片样式和高度
 */ const DataOverview = ()=>{
                _s();
                /**
   * 响应式检测
   *
   * 使用 Ant Design 的 Grid.useBreakpoint 检测当前屏幕尺寸，
   * 根据不同断点调整统计卡片的布局方式
   */ const { useBreakpoint } = _antd.Grid;
                const screens = useBreakpoint();
                /**
   * 根据屏幕尺寸决定布局方向和列数
   *
   * - xs/sm: 垂直布局，2x3 网格
   * - md+: 水平布局，1x6 排列
   */ const getLayoutDirection = ()=>{
                    if (screens.md) return 'row';
                    return 'column';
                };
                /**
   * 获取统计卡片的响应式样式
   */ const getCardStyle = ()=>{
                    if (screens.md) // 桌面端：水平排列，等宽分布
                    return {
                        flex: 1,
                        minWidth: 0
                    };
                    else // 移动端：垂直排列，固定高度
                    return {
                        marginBottom: 12
                    };
                };
                /**
   * 个人统计数据状态管理
   */ const [personalStats, setPersonalStats] = (0, _react.useState)({
                    vehicles: 0,
                    personnel: 0,
                    warnings: 0,
                    alerts: 0
                });
                const [teamCount, setTeamCount] = (0, _react.useState)(0);
                const [todoCount, setTodoCount] = (0, _react.useState)(0);
                const [statsLoading, setStatsLoading] = (0, _react.useState)(true);
                const [statsError, setStatsError] = (0, _react.useState)(null);
                // 获取统计数据
                (0, _react.useEffect)(()=>{
                    const fetchStatsData = async ()=>{
                        try {
                            // 获取个人统计数据
                            const stats = await _user.UserService.getUserPersonalStats();
                            setPersonalStats(stats);
                            // 获取团队数量
                            try {
                                const teams = await _team.TeamService.getUserTeams();
                                setTeamCount(teams.length);
                            } catch (teamError) {
                                console.error('获取团队数据失败:', teamError);
                                setTeamCount(0);
                            }
                            // 获取待办事项数量
                            try {
                                const todoStats = await _todo.TodoService.getTodoStats();
                                setTodoCount(todoStats.totalCount || 0);
                            } catch (todoError) {
                                console.error('获取待办事项数据失败:', todoError);
                                setTodoCount(0);
                            }
                            setStatsError(null);
                        } catch (error) {
                            console.error('获取统计数据失败:', error);
                            setStatsError('获取统计数据失败，请稍后重试');
                        } finally{
                            setStatsLoading(false);
                        }
                    };
                    fetchStatsData();
                }, []);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                        align: "center",
                        gap: 8,
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.BarChartOutlined, {
                                style: {
                                    fontSize: 18,
                                    color: '#2563eb'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                lineNumber: 144,
                                columnNumber: 11
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                style: {
                                    color: '#1f2937',
                                    fontWeight: 600
                                },
                                children: "数据概览"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                lineNumber: 145,
                                columnNumber: 11
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/DataOverview.tsx",
                        lineNumber: 143,
                        columnNumber: 9
                    }, void 0),
                    style: {
                        marginBottom: 16,
                        borderRadius: 16,
                        border: '1px solid rgba(37, 99, 235, 0.08)',
                        background: 'linear-gradient(135deg, #ffffff 0%, #fafbff 100%)',
                        boxShadow: '0 4px 20px rgba(37, 99, 235, 0.06)'
                    },
                    headStyle: {
                        borderBottom: '1px solid rgba(37, 99, 235, 0.08)',
                        paddingBottom: 12,
                        background: 'rgba(37, 99, 235, 0.02)'
                    },
                    bodyStyle: {
                        padding: '16px'
                    },
                    children: statsError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                        message: "数据概览加载失败",
                        description: statsError,
                        type: "error",
                        showIcon: true,
                        style: {
                            borderRadius: 8
                        }
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/DataOverview.tsx",
                        lineNumber: 165,
                        columnNumber: 9
                    }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                        spinning: statsLoading,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard.Group, {
                            direction: getLayoutDirection(),
                            style: {
                                gap: screens.md ? 16 : 12
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                    style: getCardStyle(),
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                        align: "center",
                                        justify: "flex-start",
                                        style: {
                                            height: '100%',
                                            padding: screens.md ? '16px 12px' : '12px 8px',
                                            minHeight: screens.md ? 80 : 60
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CarOutlined, {
                                                style: {
                                                    color: '#2563eb',
                                                    fontSize: screens.md ? 28 : 24,
                                                    marginRight: screens.md ? 16 : 12
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 197,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                vertical: true,
                                                align: "flex-start",
                                                justify: "center",
                                                style: {
                                                    flex: 1
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            fontSize: screens.md ? 14 : 12,
                                                            color: '#666',
                                                            marginBottom: 4
                                                        },
                                                        children: "车辆"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 206,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            color: '#2563eb',
                                                            fontSize: screens.md ? 36 : 28,
                                                            fontWeight: 700,
                                                            lineHeight: 1
                                                        },
                                                        children: personalStats.vehicles
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 215,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 205,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 187,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 184,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                    style: getCardStyle(),
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                        align: "center",
                                        justify: "flex-start",
                                        style: {
                                            height: '100%',
                                            padding: screens.md ? '16px 12px' : '12px 8px',
                                            minHeight: screens.md ? 80 : 60
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UsergroupAddOutlined, {
                                                style: {
                                                    color: '#059669',
                                                    fontSize: screens.md ? 28 : 24,
                                                    marginRight: screens.md ? 16 : 12
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 243,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                vertical: true,
                                                align: "flex-start",
                                                justify: "center",
                                                style: {
                                                    flex: 1
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            fontSize: screens.md ? 14 : 12,
                                                            color: '#666',
                                                            marginBottom: 4
                                                        },
                                                        children: "人员"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 252,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            color: '#059669',
                                                            fontSize: screens.md ? 36 : 28,
                                                            fontWeight: 700,
                                                            lineHeight: 1
                                                        },
                                                        children: personalStats.personnel
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 261,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 251,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 233,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 230,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                    style: getCardStyle(),
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                        align: "center",
                                        justify: "flex-start",
                                        style: {
                                            height: '100%',
                                            padding: screens.md ? '16px 12px' : '12px 8px',
                                            minHeight: screens.md ? 80 : 60
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                                style: {
                                                    color: '#d97706',
                                                    fontSize: screens.md ? 28 : 24,
                                                    marginRight: screens.md ? 16 : 12
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 289,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                vertical: true,
                                                align: "flex-start",
                                                justify: "center",
                                                style: {
                                                    flex: 1
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            fontSize: screens.md ? 14 : 12,
                                                            color: '#666',
                                                            marginBottom: 4
                                                        },
                                                        children: "预警"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 298,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            color: '#d97706',
                                                            fontSize: screens.md ? 36 : 28,
                                                            fontWeight: 700,
                                                            lineHeight: 1
                                                        },
                                                        children: personalStats.warnings
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 307,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 297,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 279,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 276,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                    style: getCardStyle(),
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                        align: "center",
                                        justify: "flex-start",
                                        style: {
                                            height: '100%',
                                            padding: screens.md ? '16px 12px' : '12px 8px',
                                            minHeight: screens.md ? 80 : 60
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.AlertOutlined, {
                                                style: {
                                                    color: '#dc2626',
                                                    fontSize: screens.md ? 28 : 24,
                                                    marginRight: screens.md ? 16 : 12
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 335,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                vertical: true,
                                                align: "flex-start",
                                                justify: "center",
                                                style: {
                                                    flex: 1
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            fontSize: screens.md ? 14 : 12,
                                                            color: '#666',
                                                            marginBottom: 4
                                                        },
                                                        children: "告警"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 344,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            color: '#dc2626',
                                                            fontSize: screens.md ? 36 : 28,
                                                            fontWeight: 700,
                                                            lineHeight: 1
                                                        },
                                                        children: personalStats.alerts
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 353,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 343,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 325,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 322,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                    style: getCardStyle(),
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                        align: "center",
                                        justify: "flex-start",
                                        style: {
                                            height: '100%',
                                            padding: screens.md ? '16px 12px' : '12px 8px',
                                            minHeight: screens.md ? 80 : 60
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                                style: {
                                                    color: '#7c3aed',
                                                    fontSize: screens.md ? 28 : 24,
                                                    marginRight: screens.md ? 16 : 12
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 381,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                vertical: true,
                                                align: "flex-start",
                                                justify: "center",
                                                style: {
                                                    flex: 1
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            fontSize: screens.md ? 14 : 12,
                                                            color: '#666',
                                                            marginBottom: 4
                                                        },
                                                        children: "团队数量"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 390,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            color: '#7c3aed',
                                                            fontSize: screens.md ? 36 : 28,
                                                            fontWeight: 700,
                                                            lineHeight: 1
                                                        },
                                                        children: teamCount
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 399,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 389,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 371,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 368,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                    style: getCardStyle(),
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                        align: "center",
                                        justify: "flex-start",
                                        style: {
                                            height: '100%',
                                            padding: screens.md ? '16px 12px' : '12px 8px',
                                            minHeight: screens.md ? 80 : 60
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                                style: {
                                                    color: '#0891b2',
                                                    fontSize: screens.md ? 28 : 24,
                                                    marginRight: screens.md ? 16 : 12
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 427,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                vertical: true,
                                                align: "flex-start",
                                                justify: "center",
                                                style: {
                                                    flex: 1
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            fontSize: screens.md ? 14 : 12,
                                                            color: '#666',
                                                            marginBottom: 4
                                                        },
                                                        children: "待办事项"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 436,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            color: '#0891b2',
                                                            fontSize: screens.md ? 36 : 28,
                                                            fontWeight: 700,
                                                            lineHeight: 1
                                                        },
                                                        children: todoCount
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 445,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 435,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 417,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 414,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/DataOverview.tsx",
                            lineNumber: 177,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/DataOverview.tsx",
                        lineNumber: 175,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/DataOverview.tsx",
                    lineNumber: 141,
                    columnNumber: 5
                }, this);
            };
            _s(DataOverview, "LQId/7cxR4zQWWfvKJt8XYGkZ0c=", true);
            _c = DataOverview;
            var _default = DataOverview;
            var _c;
            $RefreshReg$(_c, "DataOverview");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/services/team.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                TeamService: function() {
                    return TeamService;
                },
                // 导出默认实例
                default: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _request = __mako_require__("src/utils/request.ts");
            var _auth = __mako_require__("src/services/auth.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            class TeamService {
                /**
   * 创建团队
   *
   * 创建新的团队，创建者自动成为团队管理员。
   * 需要用户级别的Token（Account Token）。
   *
   * @param data 团队创建请求参数
   * @param data.name 团队名称（必填，2-50字符）
   * @param data.description 团队描述（可选）
   * @returns Promise<TeamDetailResponse> 创建的团队信息
   * @throws 当团队名称重复或用户权限不足时抛出异常
   *
   * @example
   * ```typescript
   * const newTeam = await TeamService.createTeam({
   *   name: '开发团队',
   *   description: '负责产品开发的团队'
   * });
   * console.log('团队创建成功:', newTeam.name);
   * ```
   */ static async createTeam(data) {
                    const response = await _request.apiRequest.post('/teams', data);
                    return response.data;
                }
                /**
   * 获取用户的团队列表
   *
   * 获取当前用户所属的所有团队的基本信息。
   * 需要用户级别的Token（Account Token）。
   *
   * @returns Promise<TeamDetailResponse[]> 团队列表
   * @throws 当用户未登录时抛出异常
   *
   * @example
   * ```typescript
   * const teams = await TeamService.getUserTeams();
   * console.log('用户所属团队数量:', teams.length);
   * teams.forEach(team => {
   *   console.log(`团队: ${team.name}, ID: ${team.id}`);
   * });
   * ```
   */ static async getUserTeams() {
                    const response = await _request.apiRequest.get('/teams');
                    return response.data;
                }
                /**
   * 获取用户的团队列表（包含统计数据）
   *
   * 获取当前用户所属的所有团队，包含每个团队的统计信息。
   * 用于个人中心的团队列表展示。
   *
   * @returns Promise<TeamDetailResponse[]> 带统计信息的团队列表
   * @throws 当用户未登录时抛出异常
   *
   * @example
   * ```typescript
   * const teams = await TeamService.getUserTeamsWithStats();
   * teams.forEach(team => {
   *   console.log(`团队: ${team.name}, 成员数: ${team.memberCount}`);
   * });
   * ```
   */ static async getUserTeamsWithStats() {
                    const response = await _request.apiRequest.get('/teams?includeStats=true');
                    return response.data;
                }
                /**
   * 获取当前团队详情
   *
   * 获取当前选择团队的详细信息。
   * 需要团队级别的Token（Team Token）。
   *
   * @returns Promise<TeamDetailResponse> 团队详细信息
   * @throws 当未选择团队或无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const teamDetail = await TeamService.getCurrentTeamDetail();
   * console.log('当前团队:', teamDetail.name);
   * console.log('团队描述:', teamDetail.description);
   * console.log('成员数量:', teamDetail.memberCount);
   * ```
   */ static async getCurrentTeamDetail() {
                    const response = await _request.apiRequest.get('/teams/current');
                    return response.data;
                }
                /**
   * 更新当前团队信息
   *
   * 更新当前团队的基本信息，如名称、描述等。
   * 需要团队级别的Token，且仅团队创建者有权限。
   *
   * @param data 团队更新请求参数
   * @param data.name 新的团队名称（可选）
   * @param data.description 新的团队描述（可选）
   * @returns Promise<TeamDetailResponse> 更新后的团队信息
   * @throws 当用户非团队创建者或团队名称重复时抛出异常
   *
   * @example
   * ```typescript
   * const updatedTeam = await TeamService.updateCurrentTeam({
   *   name: '新团队名称',
   *   description: '更新后的团队描述'
   * });
   * console.log('团队信息更新成功');
   * ```
   */ static async updateCurrentTeam(data) {
                    const response = await _request.apiRequest.put('/teams/current', data);
                    return response.data;
                }
                /**
   * 删除当前团队（需要 Team Token，仅创建者）
   *
   * 权限要求：
   * - 需要有效的Team Token
   * - 只有团队创建者可以执行此操作
   *
   * 删除效果：
   * - 软删除团队记录
   * - 级联删除所有团队成员关系
   * - 不可恢复
   *
   * @returns Promise<void> 删除成功时resolve
   * @throws 当权限不足或团队不存在时抛出异常
   */ static async deleteCurrentTeam() {
                    await _request.apiRequest.delete('/teams/current');
                }
                /**
   * 删除指定团队（需要切换到该团队的 Team Token，仅创建者）
   *
   * @param teamId 要删除的团队ID
   * @returns Promise<void> 删除成功时resolve
   * @throws 当权限不足或团队不存在时抛出异常
   */ static async deleteTeam(teamId) {
                    // 先切换到目标团队
                    await _auth.AuthService.selectTeam({
                        teamId
                    });
                    // 然后删除团队
                    await _request.apiRequest.delete('/teams/current');
                }
                /**
   * 退出团队（清除团队上下文）
   *
   * 通过清除团队上下文来实现退出团队的效果。
   * 用户将返回到用户级别的Token，可以重新选择团队。
   *
   * 注意：这个操作只是清除当前的团队上下文，用户仍然是团队成员，
   * 只是需要重新选择团队。如果需要完全退出团队，需要团队创建者移除该成员。
   *
   * @returns Promise<string> 新的用户级别Token
   * @throws 当用户未登录时抛出异常
   */ static async leaveTeam() {
                    return await _auth.AuthService.clearTeam();
                }
                /**
   * 获取当前团队成员列表（简单数组格式）
   *
   * 获取当前团队的所有成员，返回简单数组格式。
   * 内部调用分页接口并获取所有成员。
   *
   * @returns Promise<TeamMemberResponse[]> 团队成员列表
   * @throws 当未选择团队或无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const members = await TeamService.getCurrentTeamMembers();
   * console.log('团队成员数量:', members.length);
   * members.forEach(member => {
   *   console.log(`成员: ${member.name}, 邮箱: ${member.email}`);
   * });
   * ```
   */ static async getCurrentTeamMembers() {
                    const response = await TeamService.getTeamMembers({
                        current: 1,
                        pageSize: 1000
                    });
                    return (response === null || response === void 0 ? void 0 : response.list) || [];
                }
                /**
   * 获取当前团队成员列表（分页格式）
   *
   * 获取当前团队的成员列表，支持分页查询。
   * 需要团队级别的Token（Team Token）。
   *
   * @param params 分页查询参数（可选）
   * @param params.current 当前页码（默认1）
   * @param params.pageSize 每页大小（默认10）
   * @returns Promise<PageResponse<TeamMemberResponse>> 分页的成员列表
   * @throws 当未选择团队或无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const membersPage = await TeamService.getTeamMembers({
   *   current: 1,
   *   pageSize: 20
   * });
   *
   * console.log('总成员数:', membersPage.total);
   * console.log('当前页成员:', membersPage.list);
   * ```
   */ static async getTeamMembers(params) {
                    const response = await _request.apiRequest.get('/teams/current/members', params);
                    return response.data;
                }
                /**
   * 邀请团队成员
   *
   * 向指定邮箱发送团队邀请。被邀请人会收到邮件邀请链接。
   * 需要团队级别的Token，且仅团队创建者有权限。
   *
   * @param data 邀请请求参数
   * @param data.emails 被邀请人的邮箱列表
   * @returns Promise<void> 邀请发送成功时resolve
   * @throws 当用户非团队创建者或邮箱格式错误时抛出异常
   *
   * @example
   * ```typescript
   * await TeamService.inviteMembers({
   *   emails: ['<EMAIL>', '<EMAIL>']
   * });
   * console.log('邀请已发送');
   * ```
   */ static async inviteMembers(data) {
                    const response = await _request.apiRequest.post('/teams/current/members/invite', data);
                    return response.data;
                }
                /**
   * 移除团队成员
   *
   * 从当前团队中移除指定成员。
   * 需要团队级别的Token，且仅团队创建者有权限。
   *
   * @param memberId 要移除的成员ID
   * @returns Promise<void> 移除成功时resolve
   * @throws 当用户非团队创建者或成员不存在时抛出异常
   *
   * @example
   * ```typescript
   * await TeamService.removeMember(123);
   * console.log('成员已移除');
   * ```
   */ static async removeMember(memberId) {
                    const response = await _request.apiRequest.delete(`/teams/current/members/${memberId}`);
                    return response.data;
                }
                /**
   * 更新团队成员状态
   *
   * 更新团队成员的激活状态（启用/禁用）。
   * 需要团队级别的Token，且仅团队创建者有权限。
   *
   * @param memberId 成员ID
   * @param isActive 是否激活（true=启用，false=禁用）
   * @returns Promise<void> 更新成功时resolve
   * @throws 当用户非团队创建者或成员不存在时抛出异常
   *
   * @example
   * ```typescript
   * // 禁用成员
   * await TeamService.updateMemberStatus(123, false);
   * console.log('成员已禁用');
   *
   * // 启用成员
   * await TeamService.updateMemberStatus(123, true);
   * console.log('成员已启用');
   * ```
   */ static async updateMemberStatus(memberId, isActive) {
                    const response = await _request.apiRequest.put(`/teams/current/members/${memberId}/status?isActive=${isActive}`);
                    return response.data;
                }
                /**
   * 获取团队统计信息
   *
   * 获取当前团队的统计信息，包括成员数量、活跃成员数等。
   * 注意：当前通过团队详情和成员列表计算，等待后端提供专门的统计接口。
   *
   * @returns Promise<object> 团队统计信息
   * @returns Promise<object>.memberCount 总成员数
   * @returns Promise<object>.activeMembers 活跃成员数（状态为启用的成员）
   * @returns Promise<object>.recentActivity 最近活跃成员数（7天内有访问的成员）
   * @throws 当未选择团队或无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const stats = await TeamService.getTeamStats();
   * console.log('总成员数:', stats.memberCount);
   * console.log('活跃成员数:', stats.activeMembers);
   * console.log('最近活跃成员数:', stats.recentActivity);
   * ```
   */ static async getTeamStats() {
                    // 这里可能需要后端提供专门的统计接口
                    // 暂时通过团队详情和成员列表来计算
                    const teamDetail = await TeamService.getCurrentTeamDetail();
                    const members = await TeamService.getTeamMembers({
                        current: 1,
                        pageSize: 1000
                    });
                    const activeMembers = members.list.filter((member)=>member.isActive).length;
                    const recentActivity = members.list.filter((member)=>{
                        const lastAccess = new Date(member.lastAccessTime);
                        const weekAgo = new Date();
                        weekAgo.setDate(weekAgo.getDate() - 7);
                        return lastAccess > weekAgo;
                    }).length;
                    return {
                        memberCount: teamDetail.memberCount,
                        activeMembers,
                        recentActivity
                    };
                }
            }
            var _default = TeamService;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/services/todo.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "TodoService", {
                enumerable: true,
                get: function() {
                    return TodoService;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _request = __mako_require__("src/utils/request.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            class TodoService {
                /**
   * 获取用户的TODO列表
   */ static async getUserTodos() {
                    const response = await _request.apiRequest.get('/todos');
                    return response.data;
                }
                /**
   * 创建TODO
   */ static async createTodo(request) {
                    const response = await _request.apiRequest.post('/todos', request);
                    return response.data;
                }
                /**
   * 更新TODO
   */ static async updateTodo(id, request) {
                    const response = await _request.apiRequest.put(`/todos/${id}`, request);
                    return response.data;
                }
                /**
   * 删除TODO
   */ static async deleteTodo(id) {
                    await _request.apiRequest.delete(`/todos/${id}`);
                }
                /**
   * 获取TODO统计信息
   */ static async getTodoStats() {
                    const response = await _request.apiRequest.get('/todos/stats');
                    return response.data;
                }
            }
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '12281486594547187814';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=umi.17499660989144111243.hot-update.js.map