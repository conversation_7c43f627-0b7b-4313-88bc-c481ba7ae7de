globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/DataOverview.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _user = __mako_require__("src/services/user.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            /**
 * 数据概览卡片组件
 *
 * 使用 Ant Design Pro Components 的 StatisticCard 组件显示用户的个人统计数据，
 * 采用响应式网格布局适配不同屏幕尺寸。包括车辆、人员、预警、告警等指标的统计卡片。
 *
 * 主要功能：
 * 1. 显示车辆数量统计 - 使用车辆图标，蓝色主题
 * 2. 显示人员数量统计 - 使用用户组图标，绿色主题
 * 3. 显示预警数量统计 - 使用感叹号图标，橙色主题
 * 4. 显示告警数量统计 - 使用警告图标，红色主题
 *
 * 数据来源：
 * - 个人统计数据：通过UserService.getUserPersonalStats()获取
 *
 * 响应式布局特点：
 * - xs (< 576px): 2x2 网格布局，移动端优化
 * - sm (≥ 576px): 2x2 网格布局，小屏设备
 * - md (≥ 768px): 1x4 水平排列，平板优化
 * - lg+ (≥ 992px): 1x4 水平排列，桌面端
 * - 每个统计项都有语义化的图标和颜色主题
 * - 统一的卡片样式和高度
 */ const DataOverview = ()=>{
                _s();
                /**
   * 响应式检测
   *
   * 使用 Ant Design 的 Grid.useBreakpoint 检测当前屏幕尺寸，
   * 根据不同断点调整统计卡片的布局方式
   */ const { useBreakpoint } = _antd.Grid;
                const screens = useBreakpoint();
                /**
   * 根据屏幕尺寸决定布局方向和列数
   *
   * - xs/sm: 垂直布局，2x3 网格
   * - md+: 水平布局，1x6 排列
   */ const getLayoutDirection = ()=>{
                    if (screens.md) return 'row';
                    return 'column';
                };
                /**
   * 获取统计卡片的响应式样式
   */ const getCardStyle = ()=>{
                    if (screens.md) // 桌面端：水平排列，等宽分布
                    return {
                        flex: 1,
                        minWidth: 0
                    };
                    else // 移动端：垂直排列，固定高度
                    return {
                        marginBottom: 12
                    };
                };
                /**
   * 个人统计数据状态管理
   */ const [personalStats, setPersonalStats] = (0, _react.useState)({
                    vehicles: 0,
                    personnel: 0,
                    warnings: 0,
                    alerts: 0
                });
                const [teamCount, setTeamCount] = (0, _react.useState)(0);
                const [todoCount, setTodoCount] = (0, _react.useState)(0);
                const [statsLoading, setStatsLoading] = (0, _react.useState)(true);
                const [statsError, setStatsError] = (0, _react.useState)(null);
                // 获取统计数据
                (0, _react.useEffect)(()=>{
                    const fetchStatsData = async ()=>{
                        try {
                            // 获取个人统计数据
                            const stats = await _user.UserService.getUserPersonalStats();
                            setPersonalStats(stats);
                            // 获取团队数量
                            try {
                                const teams = await TeamService.getUserTeams();
                                setTeamCount(teams.length);
                            } catch (teamError) {
                                console.error('获取团队数据失败:', teamError);
                                setTeamCount(0);
                            }
                            // 获取待办事项数量
                            try {
                                const todoStats = await TodoService.getTodoStats();
                                setTodoCount(todoStats.totalCount || 0);
                            } catch (todoError) {
                                console.error('获取待办事项数据失败:', todoError);
                                setTodoCount(0);
                            }
                            setStatsError(null);
                        } catch (error) {
                            console.error('获取统计数据失败:', error);
                            setStatsError('获取统计数据失败，请稍后重试');
                        } finally{
                            setStatsLoading(false);
                        }
                    };
                    fetchStatsData();
                }, []);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                        align: "center",
                        gap: 8,
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.BarChartOutlined, {
                                style: {
                                    fontSize: 18,
                                    color: '#2563eb'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                lineNumber: 140,
                                columnNumber: 11
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                style: {
                                    color: '#1f2937',
                                    fontWeight: 600
                                },
                                children: "数据概览"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                lineNumber: 141,
                                columnNumber: 11
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/DataOverview.tsx",
                        lineNumber: 139,
                        columnNumber: 9
                    }, void 0),
                    style: {
                        marginBottom: 16,
                        borderRadius: 16,
                        border: '1px solid rgba(37, 99, 235, 0.08)',
                        background: 'linear-gradient(135deg, #ffffff 0%, #fafbff 100%)',
                        boxShadow: '0 4px 20px rgba(37, 99, 235, 0.06)'
                    },
                    headStyle: {
                        borderBottom: '1px solid rgba(37, 99, 235, 0.08)',
                        paddingBottom: 12,
                        background: 'rgba(37, 99, 235, 0.02)'
                    },
                    bodyStyle: {
                        padding: '16px'
                    },
                    children: statsError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                        message: "数据概览加载失败",
                        description: statsError,
                        type: "error",
                        showIcon: true,
                        style: {
                            borderRadius: 8
                        }
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/DataOverview.tsx",
                        lineNumber: 161,
                        columnNumber: 9
                    }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                        spinning: statsLoading,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard.Group, {
                            direction: getLayoutDirection(),
                            style: {
                                gap: screens.md ? 16 : 12
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                    style: getCardStyle(),
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                        align: "center",
                                        justify: "flex-start",
                                        style: {
                                            height: '100%',
                                            padding: screens.md ? '16px 12px' : '12px 8px',
                                            minHeight: screens.md ? 80 : 60
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CarOutlined, {
                                                style: {
                                                    color: '#2563eb',
                                                    fontSize: screens.md ? 28 : 24,
                                                    marginRight: screens.md ? 16 : 12
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 193,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                vertical: true,
                                                align: "flex-start",
                                                justify: "center",
                                                style: {
                                                    flex: 1
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            fontSize: screens.md ? 14 : 12,
                                                            color: '#666',
                                                            marginBottom: 4
                                                        },
                                                        children: "车辆"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 202,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            color: '#2563eb',
                                                            fontSize: screens.md ? 36 : 28,
                                                            fontWeight: 700,
                                                            lineHeight: 1
                                                        },
                                                        children: personalStats.vehicles
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 211,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 201,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 183,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 180,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                    style: getCardStyle(),
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                        align: "center",
                                        justify: "flex-start",
                                        style: {
                                            height: '100%',
                                            padding: screens.md ? '16px 12px' : '12px 8px',
                                            minHeight: screens.md ? 80 : 60
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UsergroupAddOutlined, {
                                                style: {
                                                    color: '#059669',
                                                    fontSize: screens.md ? 28 : 24,
                                                    marginRight: screens.md ? 16 : 12
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 239,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                vertical: true,
                                                align: "flex-start",
                                                justify: "center",
                                                style: {
                                                    flex: 1
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            fontSize: screens.md ? 14 : 12,
                                                            color: '#666',
                                                            marginBottom: 4
                                                        },
                                                        children: "人员"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 248,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            color: '#059669',
                                                            fontSize: screens.md ? 36 : 28,
                                                            fontWeight: 700,
                                                            lineHeight: 1
                                                        },
                                                        children: personalStats.personnel
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 257,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 247,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 229,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 226,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                    style: getCardStyle(),
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                        align: "center",
                                        justify: "flex-start",
                                        style: {
                                            height: '100%',
                                            padding: screens.md ? '16px 12px' : '12px 8px',
                                            minHeight: screens.md ? 80 : 60
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                                style: {
                                                    color: '#d97706',
                                                    fontSize: screens.md ? 28 : 24,
                                                    marginRight: screens.md ? 16 : 12
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 285,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                vertical: true,
                                                align: "flex-start",
                                                justify: "center",
                                                style: {
                                                    flex: 1
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            fontSize: screens.md ? 14 : 12,
                                                            color: '#666',
                                                            marginBottom: 4
                                                        },
                                                        children: "预警"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 294,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            color: '#d97706',
                                                            fontSize: screens.md ? 36 : 28,
                                                            fontWeight: 700,
                                                            lineHeight: 1
                                                        },
                                                        children: personalStats.warnings
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 303,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 293,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 275,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 272,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                    style: getCardStyle(),
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                        align: "center",
                                        justify: "flex-start",
                                        style: {
                                            height: '100%',
                                            padding: screens.md ? '16px 12px' : '12px 8px',
                                            minHeight: screens.md ? 80 : 60
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.AlertOutlined, {
                                                style: {
                                                    color: '#dc2626',
                                                    fontSize: screens.md ? 28 : 24,
                                                    marginRight: screens.md ? 16 : 12
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 331,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                vertical: true,
                                                align: "flex-start",
                                                justify: "center",
                                                style: {
                                                    flex: 1
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            fontSize: screens.md ? 14 : 12,
                                                            color: '#666',
                                                            marginBottom: 4
                                                        },
                                                        children: "告警"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 340,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            color: '#dc2626',
                                                            fontSize: screens.md ? 36 : 28,
                                                            fontWeight: 700,
                                                            lineHeight: 1
                                                        },
                                                        children: personalStats.alerts
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 349,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 339,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 321,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 318,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                    style: getCardStyle(),
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                        align: "center",
                                        justify: "flex-start",
                                        style: {
                                            height: '100%',
                                            padding: screens.md ? '16px 12px' : '12px 8px',
                                            minHeight: screens.md ? 80 : 60
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TeamOutlined, {
                                                style: {
                                                    color: '#7c3aed',
                                                    fontSize: screens.md ? 28 : 24,
                                                    marginRight: screens.md ? 16 : 12
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 377,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                vertical: true,
                                                align: "flex-start",
                                                justify: "center",
                                                style: {
                                                    flex: 1
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            fontSize: screens.md ? 14 : 12,
                                                            color: '#666',
                                                            marginBottom: 4
                                                        },
                                                        children: "团队数量"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 386,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            color: '#7c3aed',
                                                            fontSize: screens.md ? 36 : 28,
                                                            fontWeight: 700,
                                                            lineHeight: 1
                                                        },
                                                        children: teamCount
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 395,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 385,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 367,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 364,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                    style: getCardStyle(),
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                        align: "center",
                                        justify: "flex-start",
                                        style: {
                                            height: '100%',
                                            padding: screens.md ? '16px 12px' : '12px 8px',
                                            minHeight: screens.md ? 80 : 60
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(CalendarOutlined, {
                                                style: {
                                                    color: '#0891b2',
                                                    fontSize: screens.md ? 28 : 24,
                                                    marginRight: screens.md ? 16 : 12
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 423,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                vertical: true,
                                                align: "flex-start",
                                                justify: "center",
                                                style: {
                                                    flex: 1
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            fontSize: screens.md ? 14 : 12,
                                                            color: '#666',
                                                            marginBottom: 4
                                                        },
                                                        children: "待办事项"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 432,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                        style: {
                                                            color: '#0891b2',
                                                            fontSize: screens.md ? 36 : 28,
                                                            fontWeight: 700,
                                                            lineHeight: 1
                                                        },
                                                        children: todoCount
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 441,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 431,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 413,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 410,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/DataOverview.tsx",
                            lineNumber: 173,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/DataOverview.tsx",
                        lineNumber: 171,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/DataOverview.tsx",
                    lineNumber: 137,
                    columnNumber: 5
                }, this);
            };
            _s(DataOverview, "LQId/7cxR4zQWWfvKJt8XYGkZ0c=", true);
            _c = DataOverview;
            var _default = DataOverview;
            var _c;
            $RefreshReg$(_c, "DataOverview");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '3827655694673916692';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.17807910543295503547.hot-update.js.map